import {Modal} from '@panda-design/components';
import {IconAlert} from '@/icons/mcp';
import Footer from './Footer';
import {Content} from './styles';
import {invalidFormInfo, notSavedFormInfo} from './utils';

interface Props {
    nextLocation: string;
    onCancel: () => void;
    type: 'notSaved' | 'invalidForm';
    resetForm: () => void;
    onConfirmLeave?: () => void;
}

const LeavePageConfirmModal = ({onCancel, nextLocation, type, resetForm, onConfirmLeave}: Props) => (
    <Modal
        open
        onCancel={onCancel}
        width={450}
        style={{maxWidth: '480px'}}
        closable={false}
        footer={
            <Footer
                nextLocation={nextLocation}
                onCancel={onCancel}
                type={type}
                resetForm={resetForm}
                onConfirmLeave={onConfirmLeave}
            />
        }
    >
        <Content gap={8}>
            <IconAlert />
            <span>
                {type === 'notSaved' ? notSavedFormInfo : invalidFormInfo}
            </span>
        </Content>
    </Modal>
);

export default LeavePageConfirmModal;
